// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import {View, Text, type StyleProp, type ViewStyle} from 'react-native';

import FormattedTime from '@components/formatted_time';
import PostPriorityLabel from '@components/post_priority/post_priority_label';
import {CHANNEL, THREAD} from '@constants/screens';
import {useTheme} from '@context/theme';
import {DEFAULT_LOCALE} from '@i18n';
import {makeStyleSheetFromTheme} from '@utils/theme';
import {typography} from '@utils/typography';
import {displayUsername, getUserTimezone} from '@utils/user';
import {shouldShowDisplayName, getDisplayNameForPost} from '@utils/post';

import HeaderCommentedOn from './commented_on';
import HeaderReply from './reply';
import HeaderTag from './tag';

import type PostModel from '@typings/database/models/servers/post';
import type UserModel from '@typings/database/models/servers/user';
type HeaderProps = {
    author?: UserModel;
    commentCount: number;
    currentUser?: UserModel;
    enablePostUsernameOverride: boolean;
    isAutoResponse: boolean;
    isCRTEnabled?: boolean;
    isCustomStatusEnabled: boolean;
    isEphemeral: boolean;
    isMilitaryTime: boolean;
    isPendingOrFailed: boolean;
    isSystemPost: boolean;
    isWebHook: boolean;
    location: string;
    post: PostModel;
    previousPost?: PostModel | null;
    rootPostAuthor?: UserModel;
    showPostPriority: boolean;
    shouldRenderReplyButton?: boolean;
    teammateNameDisplay: string;
    hideGuestTags: boolean;
    isFromHome?:boolean|null
    styleProp?: StyleProp<ViewStyle> | null;
    isCurrentUser?: boolean;
}

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => {
    return {
        container: {
            flex: 1,
            marginTop: 10,            
        },
        pendingPost: {
            opacity: 0.5,
        },
        wrapper: {
            flex: 1,
            flexDirection: 'row',
            columnGap: 5,
        },
        time: {
            color: theme.centerChannelColor,
            marginTop: 5,
            position: 'absolute',
            left: 0,
            opacity: 0.5,
            ...typography('Heading', 75, 'Light'),
        },
        postPriority: {
            alignSelf: 'center',
            marginLeft: 6,
        },
        displayName: {
            color: theme.centerChannelColor,
            ...typography('Body', 100, 'SemiBold'),
            marginBottom: 2,
        },
        displayNameContainer: {
            width: '100%',
            paddingHorizontal: 20,
            marginBottom: 4,
        },
        displayNameLeft: {
            alignItems: 'flex-start',
        },
        displayNameRight: {
            alignItems: 'flex-end',
        },
    };
});

const Header = (props: HeaderProps) => {
    const {
        author, commentCount = 0, currentUser, isAutoResponse, isCRTEnabled,
        isEphemeral, isMilitaryTime, isPendingOrFailed, isSystemPost, isWebHook,
        location, post, previousPost, rootPostAuthor, showPostPriority, shouldRenderReplyButton, teammateNameDisplay, hideGuestTags,
        isFromHome=false, styleProp, isCurrentUser = false
    } = props;
    const theme = useTheme();
    const style = getStyleSheet(theme);
    const pendingPostStyle = isPendingOrFailed ? style.pendingPost : undefined;
    const isReplyPost = Boolean(post.rootId && !isEphemeral);
    const showReply = !isReplyPost && (location !== THREAD) && (shouldRenderReplyButton && (!rootPostAuthor && commentCount > 0));
    const rootAuthorDisplayName = rootPostAuthor ? displayUsername(rootPostAuthor, currentUser?.locale, teammateNameDisplay, true) : undefined;

    // Determine if we should show the display name based on time-based grouping
    const showDisplayName = isFromHome && author && shouldShowDisplayName(post, previousPost || null);
    const displayName = showDisplayName ? getDisplayNameForPost(author) : '';

    return (
        <>
            {/* Display name for first message in each time-based group */}
            {showDisplayName && displayName && (
                <View style={[
                    style.displayNameContainer,
                    isCurrentUser ? style.displayNameLeft : style.displayNameRight
                ]}>
                    <Text style={style.displayName} testID='post_header.display_name'>
                        {displayName}
                    </Text>
                </View>
            )}
            <View style={[style.container, pendingPostStyle]}>
                <View style={[style.wrapper,styleProp]}>
                    {(!isSystemPost || isAutoResponse) &&
                    <HeaderTag
                        isAutoResponder={isAutoResponse}
                        isAutomation={isWebHook || author?.isBot}
                        showGuestTag={author?.isGuest && !hideGuestTags}
                    />
                    }
                    {!isFromHome&&<FormattedTime
                        timezone={getUserTimezone(currentUser)}
                        isMilitaryTime={isMilitaryTime}
                        value={post.createAt}
                        style={style.time}
                        testID='post_header.date_time'
                    />}                    
                    {showPostPriority && post.metadata?.priority?.priority && (
                        <View style={style.postPriority}>
                            <PostPriorityLabel
                                label={post.metadata.priority.priority}
                            />
                        </View>
                    )}
                    {!isFromHome&&!isCRTEnabled && showReply && commentCount > 0 &&
                        <HeaderReply
                            commentCount={commentCount}
                            location={location}
                            post={post}
                            theme={theme}
                        />
                    }
                </View>
            </View>
            {!isFromHome&&Boolean(rootAuthorDisplayName) && location === CHANNEL &&
            <HeaderCommentedOn
                locale={currentUser?.locale || DEFAULT_LOCALE}
                name={rootAuthorDisplayName!}
                theme={theme}
            />
            }
        </>
    );
};

export default Header;
